/**
 * Research Lifecycle Hook
 *
 * Manages the complete research flow for question nodes:
 * 1. Create content record (INITIALIZED status)
 * 2. Start AI streaming with web search (PROCESSING status)
 * 3. Display streaming content in ResearchDisplay
 * 4. Save final content and mark as ACTIVE
 *
 * Key Features:
 * - Race condition prevention with execution locks
 * - Dual API support (real LLM vs simulator)
 * - Store integration for content persistence
 * - Streaming UI with useChat hook
 * - Error handling and cleanup
 *
 * Critical Race Condition Fix:
 * - Uses isExecutingRef to prevent duplicate API calls
 * - activeContentIdRef for safe async access
 * - Proper cleanup on unmount and errors
 */

import { useState, useCallback, useRef, useMemo, useEffect } from 'react'
import { useChat } from '@ai-sdk/react'
import { DefaultChatTransport } from 'ai'
import type { Msg } from '@/app/types/ai-sdk5'
import { extractTextContent } from '@/app/types/ai-sdk5'
import { useSession } from 'next-auth/react'
import { toast } from 'react-hot-toast'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { NodeContentType } from '@/app/(conv)/dragTree/[dragTreeId]/constants/node-types'
import { DragTreeNodeContentStatus } from '@prisma/client'
import { useUIStore } from '@/app/stores/ui_store'
import type { CreateResearchContentRequest } from '@/app/api/dragtree/research_create/route'

type UseResearchLifecycleProps = {
  nodeId: string
  questionText: string
  useSimulator?: boolean
}

type UseResearchLifecycleReturn = {
  isStreaming: boolean
  streamingContent: string
  startResearch: (existingContentId?: string) => Promise<void>
  activeContentId: string | null
}

/**
 * Custom hook that manages the complete research lifecycle:
 * 1. Click button -> Create content record (INITIALIZED)
 * 2. Start streaming -> Update status to PROCESSING
 * 3. Stream in UI using local state (messages[-1])
 * 4. onFinish -> Update DB to ACTIVE, update store, switch to store content
 */
export const useResearchLifecycle = ({
  nodeId,
  questionText,
}: UseResearchLifecycleProps): UseResearchLifecycleReturn => {
  const { data: session } = useSession()
  // Select only required store methods to prevent unnecessary re-renders
  const addNodeContent = useDragTreeStore(state => state.addNodeContent)
  const updateNodeContent = useDragTreeStore(state => state.updateNodeContent)
  const markNodeAsResearched = useDragTreeStore(
    state => state.markNodeAsResearched
  )
  const { useRealLLMAPI } = useUIStore()
  const [activeContentId, setActiveContentId] = useState<string | null>(null)
  const activeContentIdRef = useRef<string | null>(null)
  const isExecutingRef = useRef<boolean>(false)
  const chatIdRef = useRef<string>(`research-${nodeId}-${Date.now()}`)

  // Lock API endpoint choice at research start to prevent race conditions
  const lockedApiEndpointRef = useRef<string | null>(null)

  // Stabilize onFinish callback to prevent infinite re-renders
  const onFinish = useCallback(
    async ({ message }: { message: Msg }) => {
      console.log('✅ [Research] Streaming completed, updating store')

      const currentContentId = activeContentIdRef.current
      if (!currentContentId) {
        console.error(
          '[Research] Missing activeContentId in onFinish, cannot update store'
        )
        toast.error('Research failed - missing content ID')
        return
      }

      // First, update the content text and status in the store
      updateNodeContent(nodeId, currentContentId, {
        status: DragTreeNodeContentStatus.ACTIVE,
        contentText: extractTextContent(message),
      })

      // Mark the node as researched in the store
      markNodeAsResearched(nodeId)

      console.log(
        '🔄 [Research] Fetching final content with search metadata...'
      )

      // Then, fetch the final content from the server to get the metadata with search results
      // Add a small delay to ensure the database transaction has completed
      setTimeout(async () => {
        try {
          console.log(
            `[Research] Fetching final content for ${currentContentId}`
          )
          const response = await fetch(
            `/api/dragtree/content?contentId=${currentContentId}`
          )
          if (!response.ok) {
            throw new Error(
              `Failed to fetch final content: ${response.statusText}`
            )
          }
          const result = await response.json()

          if (result.success && result.data?.content_text) {
            console.log(
              '✅ [Research] Received final content from server, updating store.',
              {
                contentLength: result.data.content_text.length,
                hasMetadata: !!result.data.content_metadata,
                status: result.data.status,
              }
            )

            // Update the store with the final content and metadata from the DB
            updateNodeContent(nodeId, currentContentId, {
              contentText: result.data.content_text,
              metadata: result.data.content_metadata || {},
              status: result.data.status || DragTreeNodeContentStatus.ACTIVE,
            })
          } else {
            console.warn(
              '[Research] Fetched final content but no content_text found.',
              result
            )
          }
        } catch (error) {
          console.error('❌ [Research] Failed to fetch final content:', error)
          toast.error('Could not load final research content.')
        }
      }, 500) // 500ms delay to ensure DB transaction completes

      // Clear active content ID and reset execution flag
      setActiveContentId(null)
      activeContentIdRef.current = null
      isExecutingRef.current = false
      lockedApiEndpointRef.current = null // Clear locked endpoint on completion
    },
    [nodeId, updateNodeContent, markNodeAsResearched]
  )

  // Stabilize onError callback to prevent infinite re-renders
  const onError = useCallback((error: Error) => {
    console.error('❌ [Research] Streaming error:', error)
    toast.error('Research failed: ' + error.message)
    isExecutingRef.current = false
    // Clear locked endpoint on error
    lockedApiEndpointRef.current = null
  }, [])

  // Use locked endpoint if available, otherwise fall back to current UI state
  const currentApiEndpoint = useRealLLMAPI
    ? '/api/dragtree/research_generate'
    : '/api/dragtree/research_generate-simulator'

  const effectiveApiEndpoint =
    lockedApiEndpointRef.current ?? currentApiEndpoint

  // Stabilize chatOptions to prevent useChat from re-initializing
  const chatOptions = useMemo(
    () => ({
      api: effectiveApiEndpoint,
      id: chatIdRef.current,
      onFinish,
      onError,
      // Throttle streaming updates to prevent React "Maximum update depth exceeded" error
      // Note: throttleMs not available in this AI SDK version
    }),
    [effectiveApiEndpoint, onFinish, onError]
  )

  const [streamingContentState, setStreamingContentState] = useState<string>('')

  // Use DefaultChatTransport but with proper AI SDK 5 configuration
  const chat = useChat<Msg>({
    transport: new DefaultChatTransport({
      api: effectiveApiEndpoint,
    }),
    id: chatIdRef.current,
    onFinish,
    onError,
    // AI SDK 5 performance optimizations
    experimental_throttle: 16, // ~60fps for smooth UI updates
  })

  // Extract properties for AI SDK 5 compatibility
  const { messages, sendMessage, status } = chat
  const isLoading = status === 'streaming' || status === 'submitted'

  // Create append function for compatibility with sendMessage API
  const append = useCallback(
    (
      message: { role: 'user' | 'assistant'; content: string },
      options?: any
    ) => {
      if (message.role === 'user') {
        sendMessage({ text: message.content }, options)
      }
    },
    [sendMessage]
  )
  useEffect(() => {
    const assistantMessage = messages.filter(m => m.role === 'assistant').at(-1)

    if (assistantMessage) {
      const content = extractTextContent(assistantMessage)
      if (content && content !== streamingContentState) {
        setStreamingContentState(content)
      }
    }
  }, [messages, streamingContentState])

  // Monitor tool messages for real-time search results - optimized to prevent re-render issues
  const lastToolCountRef = useRef(0)

  useEffect(() => {
    const contentId = activeContentIdRef.current
    if (!contentId) return

    // Count tool invocations to detect new ones without processing array every time
    const toolCount = messages.filter(msg => 'toolInvocations' in msg).length

    // Only process if we have new tool messages
    if (toolCount > lastToolCountRef.current) {
      lastToolCountRef.current = toolCount

      // Find the latest tool message
      const latestToolMessage = messages.findLast(
        msg => 'toolInvocations' in msg
      )
      if (latestToolMessage && (latestToolMessage as any).toolInvocations) {
        const invocations = (latestToolMessage as any).toolInvocations

        for (const inv of invocations) {
          if (inv.toolName === 'webSearch' && inv.result && inv.args?.query) {
            console.log(`[Research] Real-time search: "${inv.args.query}"`)
            // Just log for now - full results will be handled by API onFinish
            // This gives us the real-time "Searching..." feedback without store updates
          }
        }
      }
    }
  }, [messages.length, nodeId]) // Only depend on messages.length, not the full array

  const streamingContent = streamingContentState

  const startResearch = useCallback(
    async (existingContentId?: string) => {
      if (!session?.user?.id) {
        toast.error('Please log in to start research.')
        return
      }
      if (isExecutingRef.current) {
        console.warn('🚨 [Research] Already executing, skipping duplicate call')
        return
      }

      isExecutingRef.current = true
      let contentId: string | null = existingContentId || null

      try {
        console.log('🔬 [Research] Starting research lifecycle for:', nodeId)

        // Lock the API endpoint at research start to prevent race conditions
        lockedApiEndpointRef.current = currentApiEndpoint
        console.log(
          `🔒 [Research] Locked API endpoint: ${lockedApiEndpointRef.current}`
        )

        // Only create content if not provided
        if (!contentId) {
          const createPayload: CreateResearchContentRequest = {
            dragTreeNodeId: nodeId,
            questionText,
            researchType: 'QUICK_RESEARCH',
          }

          const createResponse = await fetch('/api/dragtree/research_create', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(createPayload),
          })

          if (!createResponse.ok) {
            throw new Error('Failed to create research content record')
          }

          const createResult = await createResponse.json()
          contentId = createResult.data.contentId

          if (!contentId) {
            throw new Error('Failed to get content ID from response')
          }

          console.log('📝 [Research] Created content record:', contentId)

          addNodeContent(nodeId, {
            contentId,
            contentType: NodeContentType.QUICK_RESEARCH,
            contentVersion: 'v1',
            status: DragTreeNodeContentStatus.INITIALIZED,
            contentText: '',
            metadata: { questionText, originalQuestion: questionText },
          })
        } else {
          console.log('📝 [Research] Using existing content record:', contentId)
        }

        activeContentIdRef.current = contentId
        setActiveContentId(contentId)

        // Update status to PROCESSING
        updateNodeContent(nodeId, contentId, {
          status: DragTreeNodeContentStatus.PROCESSING,
        })

        const researchPayload = {
          contentId,
          questionText,
          researchType: 'QUICK_RESEARCH',
        }
        append(
          { role: 'user', content: questionText },
          { body: researchPayload }
        )
      } catch (error) {
        console.error('❌ [Research] Error in research setup:', error)
        toast.error('Failed to start research')
        // Clean up on setup error
        if (contentId) {
          updateNodeContent(nodeId, contentId, {
            status: DragTreeNodeContentStatus.INACTIVE,
          })
        }
        activeContentIdRef.current = null
        setActiveContentId(null)
        isExecutingRef.current = false
        lockedApiEndpointRef.current = null // Clear locked endpoint on error
      }
    },
    [
      nodeId,
      questionText,
      session?.user?.id,
      addNodeContent,
      updateNodeContent,
      append,
      currentApiEndpoint, // Include this to ensure endpoint locking works correctly
    ]
  )

  return {
    isStreaming: isLoading,
    streamingContent,
    startResearch,
    activeContentId,
  }
}
