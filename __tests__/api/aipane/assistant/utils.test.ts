/**
 * Basic tests for buildContextMessagesV2 to prevent regression
 */

import { buildContextMessagesV2 } from '@/app/api/aipane/assistant/utils'

// Mock dependencies
jest.mock('@/app/libs/prismadb', () => ({
  __esModule: true,  
  default: {
    aiMessage: {
      findMany: jest.fn(),
    },
    aiConversation: {
      findUnique: jest.fn(),
    },
  },
}))

jest.mock('@/libs/cache/conversation-cache', () => ({
  getCachedMessages: jest.fn(),
}))

describe('buildContextMessagesV2', () => {
  afterEach(() => {
    jest.clearAllMocks()
  })

  it('should build messages with language instruction', async () => {
    const { getCachedMessages } = require('@/libs/cache/conversation-cache')
    getCachedMessages.mockReturnValue([])
    
    const result = await buildContextMessagesV2(
      'thread_test123',
      { role: 'user', content: 'Hello' },
      undefined, // directContext
      'Please respond in French.' // languageInstruction
    )

    expect(result.success).toBe(true)
    expect(result.data?.messages).toHaveLength(2) // system + user
    expect(result.data?.messages[0].role).toBe('system')
    expect(result.data?.messages[0].content).toContain('Please respond in French.')
    expect(result.data?.messages[1].role).toBe('user')
    expect(result.data?.messages[1].content).toBe('Hello')
  })

  it('should build messages with direct context', async () => {
    const { getCachedMessages } = require('@/libs/cache/conversation-cache')
    getCachedMessages.mockReturnValue([])
    
    const result = await buildContextMessagesV2(
      'thread_test123',
      { role: 'user', content: 'Hello' },
      'This is test context', // directContext
      'Please respond in Spanish.' // languageInstruction
    )

    expect(result.success).toBe(true)
    expect(result.data?.messages[0].content).toContain('This is test context')
    expect(result.data?.messages[0].content).toContain('Please respond in Spanish.')
  })

  it('should handle empty conversation (new chat)', async () => {
    const { getCachedMessages } = require('@/libs/cache/conversation-cache')
    getCachedMessages.mockReturnValue([])
    
    const result = await buildContextMessagesV2(
      'thread_new123',
      { role: 'user', content: 'First message' }
    )

    expect(result.success).toBe(true)
    expect(result.data?.messages).toHaveLength(2) // system + user
    expect(result.data?.truncated).toBe(false)
  })

  it('should work without any contextIds functionality', async () => {
    const { getCachedMessages } = require('@/libs/cache/conversation-cache')
    getCachedMessages.mockReturnValue([])
    
    // Test that system works perfectly with just directContext
    const result = await buildContextMessagesV2(
      'thread_test123',
      { role: 'user', content: 'Test message' },
      'Direct context works great!',
      'Please respond in English.'
    )

    expect(result.success).toBe(true)
    expect(result.data?.messages[0].content).toContain('Direct context works great!')
    expect(result.data?.messages[0].content).toContain('Please respond in English.')
    // Should not have contextIds in result since that functionality is deprecated
    expect(result.data).not.toHaveProperty('contextIds')
  })
})